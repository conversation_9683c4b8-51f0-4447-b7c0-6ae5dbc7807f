'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { Loader2, AlertCircle, Cable, Info, Bug, XCircle } from 'lucide-react'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { Cavo, ParcoCavo } from '@/types'
import CaviDebugDialog from '@/components/debug/CaviDebugDialog'
import {
  isCableInstalled,
  isCableSpare,
  isCompatible,
  REEL_STATES
} from '@/utils/bobineUtils'

interface AggiungiCaviDialogProps {
  open: boolean
  onClose: () => void
  bobina: ParcoCavo | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

interface CavoConMetri extends Cavo {
  metri_inseriti?: number
  _isIncompatible?: boolean
}

export default function AggiungiCaviDialog({
  open,
  onClose,
  bobina,
  onSuccess,
  onError
}: AggiungiCaviDialogProps) {
  const { cantiere } = useAuth()
  const [caviLoading, setCaviLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  
  // Stati per i cavi
  const [caviCompatibili, setCaviCompatibili] = useState<CavoConMetri[]>([])
  const [caviIncompatibili, setCaviIncompatibili] = useState<CavoConMetri[]>([])
  const [caviSelezionati, setCaviSelezionati] = useState<CavoConMetri[]>([])
  const [caviMetri, setCaviMetri] = useState<Record<string, string>>({})
  
  // Stati per validazione
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [warnings, setWarnings] = useState<Record<string, string>>({})
  
  // Debug
  const [showDebugDialog, setShowDebugDialog] = useState(false)

  // Toggle per compatibili/incompatibili
  const [showCompatibili, setShowCompatibili] = useState(true)

  // TEST DIRETTO: Carica i cavi e mostra tutto
  const loadCavi = async () => {
    if (!cantiere) {
      console.log('❌ NESSUN CANTIERE')
      return
    }

    try {
      setCaviLoading(true)
      console.log('🔍 INIZIO TEST - Chiamata API...')

      // TEST AUTENTICAZIONE
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null
      console.log('🔑 TOKEN PRESENTE:', !!token)
      if (token) {
        console.log('🔑 TOKEN (primi 50 char):', token.substring(0, 50) + '...')
      }

      console.log('🔍 CANTIERE ID:', cantiere.id_cantiere)
      console.log('🔍 URL CHIAMATA:', `/api/cavi/${cantiere.id_cantiere}`)

      // TEST DIRETTO: Prova a chiamare l'API backend direttamente
      console.log('🔍 TEST DIRETTO API BACKEND...')
      try {
        const directResponse = await fetch(`http://localhost:8001/api/cavi/${cantiere.id_cantiere}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
        console.log('🔍 RISPOSTA DIRETTA STATUS:', directResponse.status)
        if (directResponse.ok) {
          const directData = await directResponse.json()
          console.log('🔍 RISPOSTA DIRETTA OK:', directData.length, 'cavi')
        } else {
          console.log('🔍 RISPOSTA DIRETTA ERROR:', await directResponse.text())
        }
      } catch (directError) {
        console.log('🔍 ERRORE CHIAMATA DIRETTA:', directError)
      }

      const caviData = await caviApi.getCavi(cantiere.id_cantiere)
      console.log('✅ API RISPOSTA RICEVUTA')
      console.log('✅ TIPO RISPOSTA:', typeof caviData)
      console.log('✅ È ARRAY?:', Array.isArray(caviData))
      console.log('📊 TOTALE CAVI:', caviData?.length || 0)

      if (!caviData) {
        console.log('❌ RISPOSTA NULL/UNDEFINED')
        setCaviCompatibili([])
        setCaviIncompatibili([])
        return
      }

      if (!Array.isArray(caviData)) {
        console.log('❌ RISPOSTA NON È UN ARRAY:', caviData)
        setCaviCompatibili([])
        setCaviIncompatibili([])
        return
      }

      if (caviData.length === 0) {
        console.log('❌ ARRAY VUOTO')
        setCaviCompatibili([])
        setCaviIncompatibili([])
        return
      }

      console.log('🔍 TOTALE CAVI CARICATI:', caviData.length)

      // FILTRO COMPLETO come nella webapp originale
      const caviDisponibili = caviData.filter(cavo => {
        const metriReali = parseFloat(cavo.metratura_reale?.toString() || '0') || 0
        const metriTeorici = parseFloat(cavo.metri_teorici?.toString() || '0') || 0

        const isNotInstalled = !isCableInstalled(cavo)
        const isNotSpare = !isCableSpare(cavo)
        const hasNoRealMeters = metriReali === 0
        const hasTheoreticalMeters = metriTeorici > 0

        const isAvailable = isNotInstalled && isNotSpare && hasNoRealMeters && hasTheoreticalMeters

        console.log(`Cavo ${cavo.id_cavo}:`, {
          stato: cavo.stato_installazione,
          metriReali,
          metriTeorici,
          modificato: cavo.modificato_manualmente,
          isNotInstalled,
          isNotSpare,
          hasNoRealMeters,
          hasTheoreticalMeters,
          isAvailable
        })

        return isAvailable
      })

      console.log('📊 CAVI DISPONIBILI:', caviDisponibili.length)

      // COMPATIBILITÀ usando utility function con DEBUG DETTAGLIATO
      console.log('🔍 BOBINA DATI:', {
        tipologia: bobina.tipologia,
        sezione: bobina.sezione,
        tipologiaType: typeof bobina.tipologia,
        sezioneType: typeof bobina.sezione
      })

      const compatibili = caviDisponibili.filter(cavo => {
        const isComp = isCompatible(cavo, bobina)
        console.log(`🔍 COMPATIBILITÀ ${cavo.id_cavo}:`, {
          cavoTipologia: cavo.tipologia,
          cavoSezione: cavo.sezione,
          bobinaTipologia: bobina.tipologia,
          bobinaSezione: bobina.sezione,
          tipologiaMatch: cavo.tipologia === bobina.tipologia,
          sezioneMatch: String(cavo.sezione) === String(bobina.sezione),
          cavoSezioneString: String(cavo.sezione),
          bobinaSezioneString: String(bobina.sezione),
          isCompatible: isComp
        })
        return isComp
      })

      const incompatibili = caviDisponibili.filter(cavo => !isCompatible(cavo, bobina))

      console.log('✅ RISULTATO FINALE:')
      console.log('- Compatibili:', compatibili.length)
      console.log('- Incompatibili:', incompatibili.length)

      if (compatibili.length > 0) {
        console.log('✅ PRIMI 3 COMPATIBILI:', compatibili.slice(0, 3).map(c => ({
          id: c.id_cavo,
          tipologia: c.tipologia,
          sezione: c.sezione
        })))
      }

      if (incompatibili.length > 0) {
        console.log('❌ PRIMI 3 INCOMPATIBILI:', incompatibili.slice(0, 3).map(c => ({
          id: c.id_cavo,
          tipologia: c.tipologia,
          sezione: c.sezione
        })))
      }

      setCaviCompatibili(compatibili)
      setCaviIncompatibili(incompatibili)

    } catch (error: any) {
      console.error('❌ ERRORE COMPLETO:', error)
      console.error('❌ ERRORE MESSAGE:', error.message)
      console.error('❌ ERRORE RESPONSE:', error.response)
      console.error('❌ ERRORE STATUS:', error.response?.status)
      console.error('❌ ERRORE DATA:', error.response?.data)
      onError('Errore nel caricamento dei cavi: ' + (error.response?.data?.detail || error.message))
    } finally {
      setCaviLoading(false)
    }
  }

  // Reset quando si apre il dialog
  useEffect(() => {
    console.log('🔄 useEffect triggered:', { open, bobina: !!bobina, cantiere: !!cantiere })
    if (open && bobina && cantiere) {
      console.log('📞 Calling loadCavi...')

      // Controllo OVER: se la bobina è già OVER, non permettere operazioni
      if (bobina.stato_bobina === REEL_STATES.OVER) {
        console.log('❌ BOBINA OVER - Operazioni bloccate')
        onError('La bobina è in stato OVER e non può accettare nuovi cavi')
        onClose()
        return
      }

      setCaviSelezionati([])
      setCaviMetri({})
      setErrors({})
      setWarnings({})
      loadCavi()
    }
  }, [open, bobina, cantiere])

  // Gestisce la selezione di un cavo
  const handleCavoToggle = (cavo: CavoConMetri, isCompatible: boolean) => {
    const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)

    if (isSelected) {
      // Rimuovi dalla selezione
      setCaviSelezionati(prev => prev.filter(c => c.id_cavo !== cavo.id_cavo))
      setCaviMetri(prev => {
        const newMetri = { ...prev }
        delete newMetri[cavo.id_cavo]
        return newMetri
      })
    } else {
      // Aggiungi alla selezione
      setCaviSelezionati(prev => [...prev, cavo])
      // Imposta metri a 0 come default (requisito utente)
      setCaviMetri(prev => ({
        ...prev,
        [cavo.id_cavo]: '0'
      }))

      // Se il cavo è incompatibile, mostra un avviso
      if (!isCompatible) {
        onSuccess(`Cavo incompatibile ${cavo.id_cavo} aggiunto alla selezione`)
      }
    }
  }



  // Gestisce il cambio dei metri
  const handleMetriChange = (cavoId: string, value: string) => {
    setCaviMetri(prev => ({
      ...prev,
      [cavoId]: value
    }))

    // Validazione in tempo reale
    validateMetri(cavoId, value)
  }

  // Valida i metri inseriti con controllo OVER dinamico
  const validateMetri = (cavoId: string, value: string) => {
    const cavo = caviSelezionati.find(c => c.id_cavo === cavoId)
    if (!cavo || !bobina) return

    const metri = parseFloat(value)
    const metriTeorici = parseFloat(cavo.metri_teorici?.toString() || '0')

    // Aggiorna temporaneamente i metri per il calcolo
    const tempMetri = { ...caviMetri, [cavoId]: value }

    // Calcola metri progressivi con il nuovo valore
    let metriResiduiSimulati = bobina.metri_residui
    let cavoBloccato = false

    // Simula l'inserimento progressivo
    for (const cavoSel of caviSelezionati) {
      const metriCavo = parseFloat(tempMetri[cavoSel.id_cavo] || '0')

      if (metriCavo > 0) {
        if (metriResiduiSimulati - metriCavo < 0) {
          // Se questo è il cavo che stiamo validando e causerebbe OVER
          if (cavoSel.id_cavo === cavoId) {
            cavoBloccato = true
          }
          break
        }
        metriResiduiSimulati -= metriCavo
      }
    }

    const newErrors = { ...errors }
    const newWarnings = { ...warnings }

    // Rimuovi errori/warning precedenti per questo cavo
    delete newErrors[cavoId]
    delete newWarnings[cavoId]

    if (metri > 0) {
      if (isNaN(metri) || metri <= 0) {
        newErrors[cavoId] = 'Inserire un valore valido maggiore di 0'
      } else if (cavoBloccato) {
        newErrors[cavoId] = `Metri causerebbero OVER (disponibili: ${metriResiduiSimulati + metri}m)`
      } else if (metri > metriTeorici * 1.1) {
        newErrors[cavoId] = `Metri eccessivi (max consigliato: ${(metriTeorici * 1.1).toFixed(1)}m)`
      } else if (metri > metriTeorici) {
        newWarnings[cavoId] = `Metri superiori ai teorici (${metriTeorici}m)`
      }
    }

    setErrors(newErrors)
    setWarnings(newWarnings)

    // Rivalidazione immediata per aggiornare lo stato degli altri cavi
    // Rimuovo setTimeout per evitare race conditions
    setTimeout(() => validateAllMetri(), 0)
  }

  // Calcola metri reali progressivi per controllo OVER dinamico
  const calculateProgressiveMeters = (): {
    metriResiduiSimulati: number;
    caviValidi: string[];
    caviBloccati: string[]
  } => {
    if (!bobina) {
      return { metriResiduiSimulati: 0, caviValidi: [], caviBloccati: [] }
    }

    let metriResiduiSimulati = bobina.metri_residui
    const caviValidi: string[] = []
    const caviBloccati: string[] = []

    // Mantieni l'ordine di selezione dell'utente per inserimento sequenziale
    const caviOrdinati = [...caviSelezionati]

    for (const cavo of caviOrdinati) {
      const metri = parseFloat(caviMetri[cavo.id_cavo] || '0')

      if (metri > 0) {
        // Se questo cavo causerebbe OVER, bloccalo
        if (metriResiduiSimulati - metri < 0) {
          caviBloccati.push(cavo.id_cavo)
          // Sottrai comunque i metri per calcolo corretto dei successivi
          metriResiduiSimulati -= metri
        } else {
          metriResiduiSimulati -= metri
          caviValidi.push(cavo.id_cavo)
        }
      } else {
        // Cavi senza metri: disponibili solo se ci sono metri residui
        if (metriResiduiSimulati > 0) {
          caviValidi.push(cavo.id_cavo)
        } else {
          caviBloccati.push(cavo.id_cavo)
        }
      }
    }

    return { metriResiduiSimulati, caviValidi, caviBloccati }
  }

  // Valida tutti i metri con controlli OVER dinamici
  const validateAllMetri = (): boolean => {
    if (!bobina) return false

    let isValid = true
    const newErrors: Record<string, string> = {}
    const newWarnings: Record<string, string> = {}

    // Controllo OVER: se la bobina è già OVER, blocca tutto
    if (bobina.stato_bobina === REEL_STATES.OVER) {
      caviSelezionati.forEach(cavo => {
        newErrors[cavo.id_cavo] = 'La bobina è in stato OVER e non può accettare nuovi cavi'
      })
      setErrors(newErrors)
      return false
    }

    // Calcola metri progressivi per controllo OVER dinamico
    const { caviBloccati } = calculateProgressiveMeters()

    // Validazione individuale
    caviSelezionati.forEach(cavo => {
      const metri = parseFloat(caviMetri[cavo.id_cavo] || '0')
      const metriTeorici = parseFloat(cavo.metri_teorici?.toString() || '0')

      // Controllo se il cavo è bloccato per OVER
      if (caviBloccati.includes(cavo.id_cavo)) {
        newErrors[cavo.id_cavo] = 'Cavo bloccato: inserimento causerebbe stato OVER'
        isValid = false
        return
      }

      // Validazione standard
      if (metri > 0) {
        if (isNaN(metri) || metri <= 0) {
          newErrors[cavo.id_cavo] = 'Inserire un valore valido maggiore di 0'
          isValid = false
        } else if (metri > metriTeorici * 1.1) {
          newErrors[cavo.id_cavo] = `Metri eccessivi (max consigliato: ${(metriTeorici * 1.1).toFixed(1)}m)`
          isValid = false
        } else if (metri > metriTeorici) {
          newWarnings[cavo.id_cavo] = `Metri superiori ai teorici (${metriTeorici}m)`
        }
      }
    })

    setErrors(newErrors)
    setWarnings(newWarnings)

    return isValid
  }

  // Gestisce il salvataggio
  const handleSave = async () => {
    if (!cantiere || !bobina) return

    try {
      // Controllo OVER preventivo
      if (bobina.stato_bobina === REEL_STATES.OVER) {
        onError('La bobina è in stato OVER e non può accettare nuovi cavi')
        return
      }

      // Validazione completa (include controlli OVER)
      if (!validateAllMetri()) {
        return
      }

      setSaving(true)

      // Calcola cavi validi (non bloccati e con metri > 0)
      const { caviValidi, caviBloccati } = calculateProgressiveMeters()

      // Filtra solo i cavi NON bloccati con metri inseriti
      const caviDaSalvare = caviSelezionati.filter(cavo => {
        const metri = parseFloat(caviMetri[cavo.id_cavo] || '0')
        const isBlocked = caviBloccati.includes(cavo.id_cavo)
        return metri > 0 && !isBlocked
      })

      console.log('💾 Cavi da salvare (solo validi):', caviDaSalvare.map(c => c.id_cavo))
      console.log('🚫 Cavi bloccati:', caviBloccati)

      if (caviDaSalvare.length === 0) {
        onError('Nessun cavo valido da salvare. Inserire metri per almeno un cavo non bloccato.')
        setSaving(false)
        return
      }

      // Aggiorna ogni cavo valido con calcolo progressivo per force_over
      const results = []
      const errors = []
      let metriResiduiProgressivi = bobina?.metri_residui || 0

      for (const cavo of caviDaSalvare) {
        try {
          const metriPosati = parseFloat(caviMetri[cavo.id_cavo])

          // Calcola se serve force_over basandosi sui metri residui progressivi
          const isSingleCavoOver = metriPosati > metriResiduiProgressivi
          const isIncompatible = cavo._isIncompatible || false
          const needsForceOver = isSingleCavoOver || isIncompatible

          console.log(`💾 Salvando cavo ${cavo.id_cavo}:`, {
            metriPosati,
            metriResiduiProgressivi,
            isSingleCavoOver,
            isIncompatible,
            needsForceOver
          })

          // Aggiorna i metri posati del cavo con force_over se necessario
          await caviApi.updateMetriPosati(
            cantiere.id_cantiere,
            cavo.id_cavo,
            metriPosati,
            bobina.id_bobina,
            needsForceOver
          )

          // Aggiorna metri residui progressivi per il prossimo cavo
          metriResiduiProgressivi -= metriPosati

          results.push({
            cavo: cavo.id_cavo,
            metriPosati,
            success: true
          })
        } catch (error: any) {
          console.error(`Errore aggiornamento cavo ${cavo.id_cavo}:`, error)
          const errorDetail = error.response?.data?.detail || error.message || 'Errore sconosciuto'

          // Gestione errori specifici
          let userFriendlyError = errorDetail
          if (errorDetail.includes('SPARE')) {
            userFriendlyError = `Cavo ${cavo.id_cavo} è marcato come SPARE`
          } else if (errorDetail.includes('metri posati non possono essere negativi')) {
            userFriendlyError = `Metri non validi per cavo ${cavo.id_cavo}`
          } else if (errorDetail.includes('non trovato')) {
            userFriendlyError = `Cavo ${cavo.id_cavo} non trovato`
          }

          errors.push({
            cavo: cavo.id_cavo,
            error: userFriendlyError
          })
        }
      }

      // Gestione del risultato
      if (errors.length === 0) {
        onSuccess(`${results.length} cavi aggiornati con successo`)
        onClose()
      } else if (results.length > 0) {
        onSuccess(`${results.length} cavi aggiornati con successo, ${errors.length} falliti`)
        onError(`Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`)
        onClose()
      } else {
        onError(`Nessun cavo aggiornato. Errori: ${errors.map(e => `${e.cavo}: ${e.error}`).join(', ')}`)
      }
    } catch (error: any) {
      console.error('Errore nel salvataggio:', error)
      onError('Errore durante il salvataggio dei cavi')
    } finally {
      setSaving(false)
    }
  }

  const handleClose = () => {
    if (!saving) {
      setCaviSelezionati([])
      setCaviMetri({})
      setErrors({})
      setWarnings({})
      onClose()
    }
  }

  if (!bobina) return null

  const getBobinaNumber = (idBobina: string) => {
    const match = idBobina.match(/C\d+_B(\d+)/)
    return match ? match[1] : idBobina
  }

  // Renderizza la lista dei cavi
  const renderCaviList = (cavi: CavoConMetri[], isCompatible: boolean) => {
    console.log('🎨 RENDER CAVI LIST:', {
      isCompatible,
      caviLength: cavi.length,
      primi3Cavi: cavi.slice(0, 3).map(c => ({
        id: c.id_cavo,
        tipologia: c.tipologia,
        sezione: c.sezione
      }))
    })

    if (cavi.length === 0) {
      return (
        <div className="h-[250px] flex items-center justify-center text-gray-500">
          <div className="text-center">
            <Cable className="h-6 w-6 mx-auto mb-2 opacity-50" />
            <div className="text-sm">Nessun cavo {isCompatible ? 'compatibile' : 'incompatibile'}</div>
            <div className="text-xs mt-1 text-gray-400">
              {isCompatible
                ? `${bobina?.tipologia} / ${bobina?.sezione}`
                : 'Tipologia o formazione diverse'
              }
            </div>
          </div>
        </div>
      )
    }

    // Calcola stato OVER per evidenziare cavi bloccati
    const progressiveResult = bobina ? calculateProgressiveMeters() : { caviValidi: [] as string[], caviBloccati: [] as string[] }
    const { caviBloccati } = progressiveResult

    return (
      <div className="space-y-2 h-[250px] overflow-y-auto">
        {cavi.map(cavo => {
          const isSelected = caviSelezionati.some(c => c.id_cavo === cavo.id_cavo)
          const metri = caviMetri[cavo.id_cavo] || ''
          const hasError = errors[cavo.id_cavo]
          const hasWarning = warnings[cavo.id_cavo]
          const isBlocked = caviBloccati.includes(cavo.id_cavo)

          return (
            <Card key={cavo.id_cavo} className={`p-1.5 ${
              isSelected ? 'ring-2 ring-blue-500' : ''
            } ${isBlocked ? 'bg-red-50 border-red-200' : ''}`}>
              <div className="flex items-start gap-2">
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={() => handleCavoToggle(cavo, isCompatible)}
                  className="mt-0.5 scale-90"
                />

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-1 mb-0.5">
                    <span className="font-medium text-sm">{cavo.id_cavo}</span>
                    <Badge variant="outline" className="text-xs px-1 py-0 h-4">
                      {cavo.tipologia}
                    </Badge>
                    <Badge variant="outline" className="text-xs px-1 py-0 h-4">
                      {cavo.sezione}
                    </Badge>
                    {!isCompatible && (
                      <Badge variant="destructive" className="text-xs px-1 py-0 h-4">
                        Inc.
                      </Badge>
                    )}
                    {isBlocked && (
                      <Badge variant="outline" className="text-xs px-1 py-0 h-4 border-red-500 text-red-600">
                        Bloc.
                      </Badge>
                    )}
                  </div>

                  <div className="grid grid-cols-3 gap-1 text-xs text-gray-600 mb-0.5">
                    <div>
                      <span className="font-medium">T:</span> {cavo.metri_teorici}m
                    </div>
                    <div>
                      <span className="font-medium">P:</span> {cavo.metri_posati}m
                    </div>
                    <div>
                      <span className="font-medium">B:</span> {cavo.id_bobina ? cavo.id_bobina.split('_B')[1] || 'N/A' : '-'}
                    </div>
                  </div>

                  {isSelected && (
                    <div className="space-y-0.5">
                      <div className="flex items-center gap-1">
                        <Label htmlFor={`metri-${cavo.id_cavo}`} className="text-xs">
                          Metri:
                        </Label>
                        <Input
                          id={`metri-${cavo.id_cavo}`}
                          type="number"
                          step="0.1"
                          min="0"
                          value={metri}
                          onChange={(e) => handleMetriChange(cavo.id_cavo, e.target.value)}
                          disabled={isBlocked}
                          className={`w-16 h-6 text-xs ${
                            hasError ? 'border-red-500' :
                            hasWarning ? 'border-yellow-500' :
                            isBlocked ? 'bg-gray-100 cursor-not-allowed' : ''
                          }`}
                          placeholder={isBlocked ? "Bloc." : "0"}
                          title={isBlocked ? "Cavo bloccato: inserimento causerebbe stato OVER" : ""}
                        />
                        <span className="text-xs text-gray-500">m</span>
                      </div>

                      {/* Indicatore metri residui progressivi */}
                      {isSelected && (
                        <div className="text-xs text-gray-500">
                          {(() => {
                            const { metriResiduiSimulati } = calculateProgressiveMeters()
                            const metriUsati = (bobina?.metri_residui || 0) - metriResiduiSimulati
                            const isOverState = metriResiduiSimulati < 0
                            return (
                              <span className={isOverState ? 'text-red-600 font-medium' : 'text-gray-500'}>
                                Usati: {metriUsati}m / {bobina?.metri_residui || 0}m
                                {isOverState && ` (OVER: ${Math.abs(metriResiduiSimulati)}m)`}
                              </span>
                            )
                          })()}
                        </div>
                      )}

                      {hasError && (
                        <div className="text-xs text-red-600 flex items-center gap-1">
                          <AlertCircle className="h-2 w-2 flex-shrink-0" />
                          <span className="truncate text-xs">{hasError}</span>
                        </div>
                      )}

                      {hasWarning && !hasError && (
                        <div className="text-xs text-yellow-600 flex items-center gap-1">
                          <Info className="h-2 w-2 flex-shrink-0" />
                          <span className="truncate text-xs">{hasWarning}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Card>
          )
        })}
      </div>
    )
  }

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent
          className="h-[85vh] overflow-hidden"
          style={{
            width: '700px !important',
            maxWidth: '95vw !important',
            minWidth: '650px'
          }}
        >
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Cable className="h-5 w-5" />
                Aggiungi cavi alla bobina {getBobinaNumber(bobina.id_bobina)}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  console.log('🐛 Debug button clicked!')
                  setShowDebugDialog(true)
                }}
                className="flex items-center gap-1"
              >
                <Bug className="h-4 w-4" />
                Debug
              </Button>
            </DialogTitle>
            <DialogDescription>
              Seleziona i cavi da associare alla bobina e inserisci i metri posati
            </DialogDescription>
          </DialogHeader>

          <div className="flex flex-col h-full">
            {/* Informazioni bobina - Sezione fissa */}
            <div className="flex-shrink-0 mb-3">
              <Card>
                <CardContent className="pt-3 pb-2">
                  <div className="grid grid-cols-4 gap-2 text-sm">
                    <div>
                      <Label className="text-xs text-gray-600">Bobina</Label>
                      <div className="font-medium text-sm">{getBobinaNumber(bobina.id_bobina)}</div>
                    </div>
                    <div>
                      <Label className="text-xs text-gray-600">Tipologia</Label>
                      <div className="font-medium text-sm">{bobina.tipologia}</div>
                    </div>
                    <div>
                      <Label className="text-xs text-gray-600">Formazione</Label>
                      <div className="font-medium text-sm">{bobina.sezione}</div>
                    </div>
                    <div>
                      <Label className="text-xs text-gray-600">Metri Residui</Label>
                      <div className="font-medium text-sm">{bobina.metri_residui}m</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Avvisi - Sezione fissa compatta */}
            <div className="flex-shrink-0 space-y-2 mb-3">
              {bobina.stato_bobina === REEL_STATES.OVER && (
                <Alert className="border-red-500 bg-red-50 py-2">
                  <XCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800 text-sm">
                    <strong>Bobina OVER</strong> - Operazione bloccata ({bobina.metri_residui}m)
                  </AlertDescription>
                </Alert>
              )}

              {bobina.stato_bobina === REEL_STATES.TERMINATA && (
                <Alert className="border-orange-500 bg-orange-50 py-2">
                  <AlertCircle className="h-4 w-4 text-orange-600" />
                  <AlertDescription className="text-orange-800 text-sm">
                    <strong>Bobina terminata</strong> - Nessun metro disponibile
                  </AlertDescription>
                </Alert>
              )}

              {(() => {
                const { caviBloccati } = calculateProgressiveMeters()
                const caviBloccatiSelezionati = caviSelezionati.filter(c => caviBloccati.includes(c.id_cavo))

                if (caviBloccatiSelezionati.length > 0) {
                  return (
                    <Alert className="border-amber-500 bg-amber-50 py-2">
                      <AlertCircle className="h-4 w-4 text-amber-600" />
                      <AlertDescription className="text-amber-800 text-sm">
                        <strong>OVER attivo</strong> - {caviBloccatiSelezionati.length} cavi bloccati
                      </AlertDescription>
                    </Alert>
                  )
                }
                return null
              })()}
            </div>

            {/* Sezione principale - Area scrollabile */}
            <div className="flex-1 min-h-0">
              {caviLoading && (
                <div className="flex items-center justify-center h-full">
                  <Loader2 className="h-6 w-6 animate-spin mr-2" />
                  <span>Caricamento cavi...</span>
                </div>
              )}

              {!caviLoading && (
                <div className="h-full flex flex-col">
                  {/* Toggle Switch compatto */}
                  <div className="flex-shrink-0 flex items-center justify-center gap-3 py-2">
                    <span className={`text-sm font-medium transition-colors ${showCompatibili ? 'text-blue-600' : 'text-gray-500'}`}>
                      Compatibili ({caviCompatibili.length})
                    </span>
                    <Switch
                      checked={!showCompatibili}
                      onCheckedChange={(checked: boolean) => setShowCompatibili(!checked)}
                      className="scale-75"
                    />
                    <span className={`text-sm font-medium transition-colors ${!showCompatibili ? 'text-blue-600' : 'text-gray-500'}`}>
                      Incompatibili ({caviIncompatibili.length})
                    </span>
                  </div>

                  {/* Contenuto dinamico */}
                  <div className="flex-1 mt-2 space-y-2">
                    {(() => {
                      console.log('🎯 RENDERING DEBUG:', {
                        showCompatibili,
                        compatibiliLength: caviCompatibili.length,
                        incompatibiliLength: caviIncompatibili.length,
                        renderingType: showCompatibili ? 'COMPATIBILI' : 'INCOMPATIBILI'
                      })
                      return null
                    })()}

                    {showCompatibili ? (
                      <>
                        <div className="text-xs text-gray-600">
                          <strong>{bobina.tipologia}</strong> / <strong>{bobina.sezione}</strong>
                        </div>
                        {renderCaviList(caviCompatibili, true)}
                      </>
                    ) : (
                      <>
                        <Alert className="py-2">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription className="text-xs">
                            Cavi incompatibili utilizzabili con <strong>force_over</strong>
                          </AlertDescription>
                        </Alert>
                        {renderCaviList(caviIncompatibili, false)}
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer fisso */}
          <DialogFooter className="flex-shrink-0 flex justify-between pt-4 border-t">
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDebugDialog(true)}
                className="flex items-center gap-1"
              >
                <Bug className="h-4 w-4" />
                Debug
              </Button>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" onClick={handleClose} disabled={saving}>
                Annulla
              </Button>
              <Button
                onClick={handleSave}
                disabled={
                  saving ||
                  caviSelezionati.length === 0 ||
                  Object.keys(errors).length > 0 ||
                  bobina.stato_bobina === REEL_STATES.OVER ||
                  bobina.stato_bobina === REEL_STATES.TERMINATA
                }
              >
                {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {saving ? 'Salvataggio...' :
                 bobina.stato_bobina === REEL_STATES.OVER ? 'Bobina OVER - Operazione bloccata' :
                 bobina.stato_bobina === REEL_STATES.TERMINATA ? 'Bobina terminata - Operazione bloccata' :
                 (() => {
                   const { caviValidi, caviBloccati } = calculateProgressiveMeters()
                   const caviConMetri = caviSelezionati.filter(c => parseFloat(caviMetri[c.id_cavo] || '0') > 0)
                   const caviValidiFiltrati = caviConMetri.filter(c => caviValidi.includes(c.id_cavo) && !caviBloccati.includes(c.id_cavo))
                   const caviBloccatiFiltrati = caviConMetri.filter(c => caviBloccati.includes(c.id_cavo))

                   if (caviBloccatiFiltrati.length > 0) {
                     return `Salva ${caviValidiFiltrati.length} cavi (${caviBloccatiFiltrati.length} bloccati)`
                   }
                   return `Salva ${caviValidiFiltrati.length} cavi`
                 })()}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog di debug */}
      <CaviDebugDialog
        open={showDebugDialog}
        onClose={() => setShowDebugDialog(false)}
        bobina={bobina}
      />
    </>
  )
}
