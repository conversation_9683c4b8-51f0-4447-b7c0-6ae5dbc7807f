{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "1b8c67627ee71a710b2ea71f2364be97", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "46393b0ce0f924067782d30c149db75ace37a9a1f9bb1a57be36be9df88590f9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "55adffffd58a791896b5b15b203cae7863b4d811b15b298f5d1a5ddfa5e94756"}}}, "sortedMiddleware": ["/"], "functions": {}}